package config

import (
	"os"
	"time"
)

// Config holds the application configuration
type Config struct {
	APIKey      string
	Model       string
	Temperature float32
	Timeout     time.Duration
}

// Load loads configuration from environment variables with sensible defaults
func Load() *Config {
	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		apiKey = os.Getenv("RESUMATTER_AI_APIKEY")
	}

	model := os.Getenv("GEMINI_MODEL")
	if model == "" {
		model = "gemini-2.0-flash"
	}

	return &Config{
		APIKey:      apiKey,
		Model:       model,
		Temperature: 0.7,
		Timeout:     30 * time.Second,
	}
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.APIKey == "" {
		return ErrMissingAPIKey
	}
	return nil
}
