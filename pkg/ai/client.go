package ai

import (
	"context"
	"encoding/json"
	"fmt"

	"google.golang.org/genai"

	"resumatter/pkg/config"
	"resumatter/pkg/types"
)

// Client handles AI operations using Google Gemini
type Client struct {
	client *genai.Client
	config *config.Config
}

// New creates a new AI client
func New(cfg *config.Config) (*Client, error) {
	if cfg.APIKey == "" {
		return nil, fmt.Errorf("API key is required (set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable)")
	}

	client, err := genai.NewClient(context.Background(), &genai.ClientConfig{
		APIKey: cfg.APIKey,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	return &Client{
		client: client,
		config: cfg,
	}, nil
}

// TailorResume tailors a resume for a specific job description
func (c *Client) TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
	systemPrompt := SystemPrompts["tailor"]
	userPrompt := fmt.Sprintf(UserPrompts["tailor"], input.BaseResume, input.JobDescription)

	config := &genai.GenerateContentConfig{
		ResponseMIMEType:  "application/json",
		Temperature:       &c.config.Temperature,
		SystemInstruction: genai.NewContentFromText(systemPrompt, genai.RoleUser),
		ResponseSchema:    c.buildTailorSchema(),
	}

	result, err := c.client.Models.GenerateContent(ctx, c.config.Model, genai.Text(userPrompt), config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	var output types.TailorResumeOutput
	if err := json.Unmarshal([]byte(result.Text()), &output); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	return &output, nil
}

// EvaluateResume evaluates a tailored resume against the original
func (c *Client) EvaluateResume(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error) {
	systemPrompt := SystemPrompts["evaluate"]
	userPrompt := fmt.Sprintf(UserPrompts["evaluate"], input.BaseResume, input.TailoredResume)

	config := &genai.GenerateContentConfig{
		ResponseMIMEType:  "application/json",
		Temperature:       &c.config.Temperature,
		SystemInstruction: genai.NewContentFromText(systemPrompt, genai.RoleUser),
		ResponseSchema:    c.buildEvaluateSchema(),
	}

	result, err := c.client.Models.GenerateContent(ctx, c.config.Model, genai.Text(userPrompt), config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	var output types.EvaluateResumeOutput
	if err := json.Unmarshal([]byte(result.Text()), &output); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	return &output, nil
}

// AnalyzeJob analyzes a job description for quality and effectiveness
func (c *Client) AnalyzeJob(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error) {
	systemPrompt := SystemPrompts["analyze"]
	userPrompt := fmt.Sprintf(UserPrompts["analyze"], input.JobDescription)

	config := &genai.GenerateContentConfig{
		ResponseMIMEType:  "application/json",
		Temperature:       &c.config.Temperature,
		SystemInstruction: genai.NewContentFromText(systemPrompt, genai.RoleUser),
		ResponseSchema:    c.buildAnalyzeSchema(),
	}

	result, err := c.client.Models.GenerateContent(ctx, c.config.Model, genai.Text(userPrompt), config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	var output types.AnalyzeJobOutput
	if err := json.Unmarshal([]byte(result.Text()), &output); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	return &output, nil
}

// Close closes the AI client
func (c *Client) Close() error {
	// Note: genai.Client doesn't have a Close method in the current version
	// This is here for future compatibility and interface consistency
	return nil
}
