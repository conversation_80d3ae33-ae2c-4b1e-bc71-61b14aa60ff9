package service

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
	"resumatter/pkg/types"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &config.Config{
				APIKey:      "test-api-key",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
			},
			wantErr: false,
		},
		{
			name: "invalid config - missing API key",
			config: &config.Config{
				APIKey:      "",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := New(tt.config)

			if tt.wantErr {
				if err == nil {
					t.<PERSON>("New() expected error but got none")
				}
				if service != nil {
					t.<PERSON>("New() expected nil service on error but got %v", service)
				}
			} else {
				if err != nil {
					t.<PERSON>rrorf("New() unexpected error: %v", err)
				}
				if service == nil {
					t.Errorf("New() expected service but got nil")
				}
			}
		})
	}
}

func TestService_Close(t *testing.T) {
	config := &config.Config{
		APIKey:      "test-api-key",
		Model:       "gemini-2.0-flash",
		Temperature: 0.7,
	}

	service, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}

	err = service.Close()
	if err != nil {
		t.Errorf("Close() unexpected error: %v", err)
	}
}

func TestService_TailorResume_FileReading(t *testing.T) {
	// Create temporary files for testing
	tmpDir := t.TempDir()
	resumeFile := filepath.Join(tmpDir, "resume.txt")
	jobFile := filepath.Join(tmpDir, "job.txt")

	resumeContent := "Software Engineer with 3 years experience"
	jobContent := "Looking for a Go developer"

	err := os.WriteFile(resumeFile, []byte(resumeContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create resume file: %v", err)
	}

	err = os.WriteFile(jobFile, []byte(jobContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create job file: %v", err)
	}

	tests := []struct {
		name       string
		resumeFile string
		jobFile    string
		wantErr    bool
		errMsg     string
	}{
		{
			name:       "valid files",
			resumeFile: resumeFile,
			jobFile:    jobFile,
			wantErr:    true, // Will fail at AI call, but files should be read successfully
			errMsg:     "failed to tailor resume",
		},
		{
			name:       "missing resume file",
			resumeFile: filepath.Join(tmpDir, "nonexistent.txt"),
			jobFile:    jobFile,
			wantErr:    true,
			errMsg:     "failed to read resume file",
		},
		{
			name:       "missing job file",
			resumeFile: resumeFile,
			jobFile:    filepath.Join(tmpDir, "nonexistent.txt"),
			wantErr:    true,
			errMsg:     "failed to read job description file",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &config.Config{
				APIKey:      "test-api-key",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
			}

			service, err := New(config)
			if err != nil {
				t.Fatalf("Failed to create service: %v", err)
			}
			defer service.Close()

			ctx := context.Background()
			_, err = service.TailorResume(ctx, tt.resumeFile, tt.jobFile)

			if tt.wantErr {
				if err == nil {
					t.Errorf("TailorResume() expected error but got none")
				} else if tt.errMsg != "" && !contains(err.Error(), tt.errMsg) {
					t.Errorf("TailorResume() error = %v, want error containing %v", err, tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("TailorResume() unexpected error: %v", err)
				}
			}
		})
	}
}

func TestService_EvaluateResume_FileReading(t *testing.T) {
	// Create temporary files for testing
	tmpDir := t.TempDir()
	baseResumeFile := filepath.Join(tmpDir, "base_resume.txt")
	tailoredResumeFile := filepath.Join(tmpDir, "tailored_resume.txt")

	baseContent := "Software Engineer with 3 years experience"
	tailoredContent := "Senior Software Engineer with 5 years experience"

	err := os.WriteFile(baseResumeFile, []byte(baseContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create base resume file: %v", err)
	}

	err = os.WriteFile(tailoredResumeFile, []byte(tailoredContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create tailored resume file: %v", err)
	}

	tests := []struct {
		name               string
		baseResumeFile     string
		tailoredResumeFile string
		wantErr            bool
		errMsg             string
	}{
		{
			name:               "valid files",
			baseResumeFile:     baseResumeFile,
			tailoredResumeFile: tailoredResumeFile,
			wantErr:            true, // Will fail at AI call, but files should be read successfully
			errMsg:             "failed to evaluate resume",
		},
		{
			name:               "missing base resume file",
			baseResumeFile:     filepath.Join(tmpDir, "nonexistent.txt"),
			tailoredResumeFile: tailoredResumeFile,
			wantErr:            true,
			errMsg:             "failed to read base resume file",
		},
		{
			name:               "missing tailored resume file",
			baseResumeFile:     baseResumeFile,
			tailoredResumeFile: filepath.Join(tmpDir, "nonexistent.txt"),
			wantErr:            true,
			errMsg:             "failed to read tailored resume file",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &config.Config{
				APIKey:      "test-api-key",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
			}

			service, err := New(config)
			if err != nil {
				t.Fatalf("Failed to create service: %v", err)
			}
			defer service.Close()

			ctx := context.Background()
			_, err = service.EvaluateResume(ctx, tt.baseResumeFile, tt.tailoredResumeFile)

			if tt.wantErr {
				if err == nil {
					t.Errorf("EvaluateResume() expected error but got none")
				} else if tt.errMsg != "" && !contains(err.Error(), tt.errMsg) {
					t.Errorf("EvaluateResume() error = %v, want error containing %v", err, tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("EvaluateResume() unexpected error: %v", err)
				}
			}
		})
	}
}

func TestService_AnalyzeJob_FileReading(t *testing.T) {
	// Create temporary files for testing
	tmpDir := t.TempDir()
	jobFile := filepath.Join(tmpDir, "job.txt")

	jobContent := "Looking for a Go developer with backend experience"

	err := os.WriteFile(jobFile, []byte(jobContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create job file: %v", err)
	}

	tests := []struct {
		name    string
		jobFile string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid file",
			jobFile: jobFile,
			wantErr: true, // Will fail at AI call, but file should be read successfully
			errMsg:  "failed to analyze job description",
		},
		{
			name:    "missing job file",
			jobFile: filepath.Join(tmpDir, "nonexistent.txt"),
			wantErr: true,
			errMsg:  "failed to read job description file",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &config.Config{
				APIKey:      "test-api-key",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
			}

			service, err := New(config)
			if err != nil {
				t.Fatalf("Failed to create service: %v", err)
			}
			defer service.Close()

			ctx := context.Background()
			_, err = service.AnalyzeJob(ctx, tt.jobFile)

			if tt.wantErr {
				if err == nil {
					t.Errorf("AnalyzeJob() expected error but got none")
				} else if tt.errMsg != "" && !contains(err.Error(), tt.errMsg) {
					t.Errorf("AnalyzeJob() error = %v, want error containing %v", err, tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("AnalyzeJob() unexpected error: %v", err)
				}
			}
		})
	}
}

func TestService_WriteOutput(t *testing.T) {
	config := &config.Config{
		APIKey:      "test-api-key",
		Model:       "gemini-2.0-flash",
		Temperature: 0.7,
	}

	service, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}
	defer service.Close()

	// Create test data
	testData := &types.TailorResumeOutput{
		TailoredResume: "Test resume content",
		ATSAnalysis: types.ATSAnalysis{
			Score:      85,
			Strengths:  "Good keywords",
			Weaknesses: "Missing skills",
		},
		JobPostingAnalysis: types.JobPostingAnalysis{
			Clarity:     "Clear and concise",
			Inclusivity: "Inclusive language used",
			Quality:     "High quality posting",
		},
	}

	tests := []struct {
		name         string
		data         any
		format       formatter.OutputFormat
		outputFile   string
		wantErr      bool
		checkContent []string
	}{
		{
			name:         "write to stdout - text format",
			data:         testData,
			format:       formatter.FormatText,
			outputFile:   "",
			wantErr:      false,
			checkContent: []string{}, // stdout output not easily testable
		},
		{
			name:         "write to stdout - json format",
			data:         testData,
			format:       formatter.FormatJSON,
			outputFile:   "",
			wantErr:      false,
			checkContent: []string{},
		},
		{
			name:         "write to file - text format",
			data:         testData,
			format:       formatter.FormatText,
			outputFile:   filepath.Join(t.TempDir(), "output.txt"),
			wantErr:      false,
			checkContent: []string{"=== TAILORED RESUME ===", "Test resume content", "Score: 85/100"},
		},
		{
			name:         "write to file - json format",
			data:         testData,
			format:       formatter.FormatJSON,
			outputFile:   filepath.Join(t.TempDir(), "output.json"),
			wantErr:      false,
			checkContent: []string{`"tailoredResume"`, `"Test resume content"`, `"score": 85`},
		},
		{
			name:       "invalid data type",
			data:       "invalid data",
			format:     formatter.FormatText,
			outputFile: "",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.WriteOutput(tt.data, tt.format, tt.outputFile)

			if tt.wantErr {
				if err == nil {
					t.Errorf("WriteOutput() expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("WriteOutput() unexpected error: %v", err)
				}

				// If writing to file, check the content
				if tt.outputFile != "" && len(tt.checkContent) > 0 {
					content, readErr := os.ReadFile(tt.outputFile)
					if readErr != nil {
						t.Fatalf("Failed to read output file: %v", readErr)
					}

					contentStr := string(content)
					for _, check := range tt.checkContent {
						if !contains(contentStr, check) {
							t.Errorf("WriteOutput() file content missing expected: %s", check)
						}
					}
				}
			}
		})
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}
