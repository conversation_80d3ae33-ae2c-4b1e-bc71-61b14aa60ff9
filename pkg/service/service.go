package service

import (
	"context"
	"fmt"

	"resumatter/pkg/ai"
	"resumatter/pkg/config"
	"resumatter/pkg/fileio"
	"resumatter/pkg/formatter"
	"resumatter/pkg/types"
)

// Service provides the core business logic for resume operations
type Service struct {
	aiClient  *ai.Client
	reader    *fileio.Reader
	writer    *fileio.Writer
	formatter *formatter.Formatter
}

// New creates a new service instance
func New(cfg *config.Config) (*Service, error) {
	if err := cfg.Validate(); err != nil {
		return nil, err
	}

	aiClient, err := ai.New(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create AI client: %w", err)
	}

	return &Service{
		aiClient:  aiClient,
		reader:    fileio.NewReader(),
		writer:    fileio.NewWriter(),
		formatter: formatter.New(),
	}, nil
}

// TailorResume tailors a resume for a specific job description
func (s *Service) TailorResume(ctx context.Context, resumeFile, jobFile string) (*types.TailorResumeOutput, error) {
	baseResume, err := s.reader.ReadFile(resumeFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read resume file: %w", err)
	}

	jobDescription, err := s.reader.ReadFile(jobFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read job description file: %w", err)
	}

	input := types.TailorResumeInput{
		BaseResume:     baseResume,
		JobDescription: jobDescription,
	}

	result, err := s.aiClient.TailorResume(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to tailor resume: %w", err)
	}

	return result, nil
}

// EvaluateResume evaluates a tailored resume against the original
func (s *Service) EvaluateResume(ctx context.Context, baseResumeFile, tailoredResumeFile string) (*types.EvaluateResumeOutput, error) {
	baseResume, err := s.reader.ReadFile(baseResumeFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read base resume file: %w", err)
	}

	tailoredResume, err := s.reader.ReadFile(tailoredResumeFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read tailored resume file: %w", err)
	}

	input := types.EvaluateResumeInput{
		BaseResume:     baseResume,
		TailoredResume: tailoredResume,
	}

	result, err := s.aiClient.EvaluateResume(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate resume: %w", err)
	}

	return result, nil
}

// AnalyzeJob analyzes a job description for quality and effectiveness
func (s *Service) AnalyzeJob(ctx context.Context, jobFile string) (*types.AnalyzeJobOutput, error) {
	jobDescription, err := s.reader.ReadFile(jobFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read job description file: %w", err)
	}

	input := types.AnalyzeJobInput{
		JobDescription: jobDescription,
	}

	result, err := s.aiClient.AnalyzeJob(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze job description: %w", err)
	}

	return result, nil
}

// WriteOutput writes the result to the specified output file or stdout
func (s *Service) WriteOutput(data any, format formatter.OutputFormat, outputFile string) error {
	output, err := s.formatter.Format(data, format)
	if err != nil {
		return fmt.Errorf("failed to format output: %w", err)
	}

	if outputFile != "" {
		if err := s.writer.WriteFile(outputFile, output); err != nil {
			return err
		}
		fmt.Printf("Output written to %s\n", outputFile)
	} else {
		s.writer.WriteToStdout(output)
	}

	return nil
}

// Close closes the service and its dependencies
func (s *Service) Close() error {
	if s.aiClient != nil {
		return s.aiClient.Close()
	}
	return nil
}
