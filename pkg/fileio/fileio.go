package fileio

import (
	"fmt"
	"io"
	"os"
)

// <PERSON> handles file reading operations
type Reader struct{}

// NewReader creates a new file reader
func NewReader() *Reader {
	return &Reader{}
}

// ReadFile reads the content of a file
func (r *Reader) ReadFile(filename string) (string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return "", fmt.Errorf("failed to open file %s: %w", filename, err)
	}
	defer file.Close()

	content, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("failed to read file %s: %w", filename, err)
	}

	return string(content), nil
}

// Writer handles file writing operations
type Writer struct{}

// NewWriter creates a new file writer
func NewWriter() *Writer {
	return &Writer{}
}

// WriteFile writes content to a file
func (w *Writer) WriteFile(filename, content string) error {
	err := os.WriteFile(filename, []byte(content), 0644)
	if err != nil {
		return fmt.E<PERSON><PERSON>("failed to write to file %s: %w", filename, err)
	}
	return nil
}

// WriteToStdout writes content to stdout
func (w *Writer) WriteToStdout(content string) {
	fmt.Print(content)
}
