package cli

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cobra"

	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
	"resumatter/pkg/service"
)

// App represents the CLI application
type App struct {
	service *service.Service
	config  *config.Config
}

// NewApp creates a new CLI application
func NewApp() (*App, error) {
	cfg := config.Load()

	svc, err := service.New(cfg)
	if err != nil {
		return nil, err
	}

	return &App{
		service: svc,
		config:  cfg,
	}, nil
}

// Close closes the application and its dependencies
func (a *App) Close() error {
	if a.service != nil {
		return a.service.Close()
	}
	return nil
}

// Execute executes the root command
func (a *App) Execute() error {
	defer a.Close()
	return a.rootCmd().Execute()
}

// rootCmd creates the root command
func (a *App) rootCmd() *cobra.Command {
	var outputFile string
	var outputFormat string

	cmd := &cobra.Command{
		Use:   "resumatter",
		Short: "AI-powered resume optimization tool",
		Long: `Resumatter is an AI-powered resume optimization tool that provides:
- Resume tailoring for specific job descriptions
- Resume evaluation for accuracy and consistency
- Job description analysis for quality and effectiveness`,
		SilenceUsage: true,
	}

	// Add persistent flags
	cmd.PersistentFlags().StringVarP(&outputFile, "output", "o", "", "Output file path (default: stdout)")
	cmd.PersistentFlags().StringVar(&outputFormat, "format", "text", "Output format: json or text")

	// Add commands
	cmd.AddCommand(a.tailorCmd(&outputFile, &outputFormat))
	cmd.AddCommand(a.evaluateCmd(&outputFile, &outputFormat))
	cmd.AddCommand(a.analyzeCmd(&outputFile, &outputFormat))

	return cmd
}

// tailorCmd creates the tailor command
func (a *App) tailorCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "tailor [resume-file] [job-description-file]",
		Short: "Tailor a resume for a specific job description",
		Long: `Tailor your resume for a specific job description using AI.
The command takes two arguments: the path to your base resume file and 
the path to the job description file. Both files should be in plain text format.`,
		Args: cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runTailor(args[0], args[1], *outputFile, *outputFormat)
		},
	}
}

// evaluateCmd creates the evaluate command
func (a *App) evaluateCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "evaluate [base-resume-file] [tailored-resume-file]",
		Short: "Evaluate a tailored resume for accuracy and consistency",
		Long: `Evaluate a tailored resume against the original to detect potential
fabrications, exaggerations, or inconsistencies. This helps ensure the
tailored resume maintains accuracy while being optimized.`,
		Args: cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runEvaluate(args[0], args[1], *outputFile, *outputFormat)
		},
	}
}

// analyzeCmd creates the analyze command
func (a *App) analyzeCmd(outputFile, outputFormat *string) *cobra.Command {
	return &cobra.Command{
		Use:   "analyze [job-description-file]",
		Short: "Analyze a job description for quality and effectiveness",
		Long: `Analyze a job description to assess its quality, clarity, inclusivity,
and effectiveness in attracting qualified candidates. Provides actionable
recommendations for improvement.`,
		Args: cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return a.runAnalyze(args[0], *outputFile, *outputFormat)
		},
	}
}

// runTailor executes the tailor command
func (a *App) runTailor(resumeFile, jobFile, outputFile, outputFormat string) error {
	fmt.Println("Tailoring resume...")

	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	result, err := a.service.TailorResume(ctx, resumeFile, jobFile)
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}

// runEvaluate executes the evaluate command
func (a *App) runEvaluate(baseResumeFile, tailoredResumeFile, outputFile, outputFormat string) error {
	fmt.Println("Evaluating resume...")

	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	result, err := a.service.EvaluateResume(ctx, baseResumeFile, tailoredResumeFile)
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}

// runAnalyze executes the analyze command
func (a *App) runAnalyze(jobFile, outputFile, outputFormat string) error {
	fmt.Println("Analyzing job description...")

	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	result, err := a.service.AnalyzeJob(ctx, jobFile)
	if err != nil {
		return err
	}

	format := a.parseOutputFormat(outputFormat)
	return a.service.WriteOutput(result, format, outputFile)
}

// parseOutputFormat parses and validates the output format
func (a *App) parseOutputFormat(format string) formatter.OutputFormat {
	switch strings.ToLower(format) {
	case "json":
		return formatter.FormatJSON
	default:
		return formatter.FormatText
	}
}
