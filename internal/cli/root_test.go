package cli

import (
	"os"
	"testing"

	"resumatter/pkg/formatter"
)

func TestNewApp(t *testing.T) {
	// Set a test API key
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	if app == nil {
		t.Fatal("NewApp() returned nil app")
	}

	if app.service == nil {
		t.Error("NewApp() service is nil")
	}

	if app.config == nil {
		t.Error("NewApp() config is nil")
	}
}

func TestParseOutputFormat(t *testing.T) {
	app := &App{}

	tests := []struct {
		input    string
		expected formatter.OutputFormat
	}{
		{"json", formatter.FormatJSON},
		{"JSON", formatter.FormatJSON},
		{"text", formatter.FormatText},
		{"TEXT", formatter.FormatText},
		{"", formatter.FormatText},
		{"invalid", formatter.FormatText},
	}

	for _, tt := range tests {
		result := app.parseOutputFormat(tt.input)
		if result != tt.expected {
			t.Errorf("parseOutputFormat(%q) = %v, want %v", tt.input, result, tt.expected)
		}
	}
}

func TestRootCmd(t *testing.T) {
	app := &App{}

	cmd := app.rootCmd()

	if cmd.Use != "resumatter" {
		t.Errorf("rootCmd().Use = %q, want %q", cmd.Use, "resumatter")
	}

	if cmd.Short == "" {
		t.Error("rootCmd().Short is empty")
	}

	// Check that subcommands are added
	expectedCommands := []string{"tailor", "evaluate", "analyze"}
	for _, expected := range expectedCommands {
		found := false
		for _, subcmd := range cmd.Commands() {
			if subcmd.Use == expected+" [resume-file] [job-description-file]" ||
				subcmd.Use == expected+" [base-resume-file] [tailored-resume-file]" ||
				subcmd.Use == expected+" [job-description-file]" {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("rootCmd() missing subcommand: %s", expected)
		}
	}
}

func TestTailorCmdArgs(t *testing.T) {
	app := &App{}
	outputFile := ""
	outputFormat := "text"

	cmd := app.tailorCmd(&outputFile, &outputFormat)

	// Test with correct number of args
	cmd.SetArgs([]string{"resume.txt", "job.txt"})
	err := cmd.Args(cmd, []string{"resume.txt", "job.txt"})
	if err != nil {
		t.Errorf("tailorCmd with 2 args should not error, got: %v", err)
	}

	// Test with incorrect number of args
	err = cmd.Args(cmd, []string{"resume.txt"})
	if err == nil {
		t.Error("tailorCmd with 1 arg should error")
	}
}

func TestEvaluateCmdArgs(t *testing.T) {
	app := &App{}
	outputFile := ""
	outputFormat := "text"

	cmd := app.evaluateCmd(&outputFile, &outputFormat)

	// Test command properties
	if cmd.Use != "evaluate [base-resume-file] [tailored-resume-file]" {
		t.Errorf("evaluateCmd().Use = %q, want %q", cmd.Use, "evaluate [base-resume-file] [tailored-resume-file]")
	}

	if cmd.Short == "" {
		t.Error("evaluateCmd().Short is empty")
	}

	// Test with correct number of args
	err := cmd.Args(cmd, []string{"base.txt", "tailored.txt"})
	if err != nil {
		t.Errorf("evaluateCmd with 2 args should not error, got: %v", err)
	}

	// Test with incorrect number of args
	err = cmd.Args(cmd, []string{"base.txt"})
	if err == nil {
		t.Error("evaluateCmd with 1 arg should error")
	}
}

func TestAnalyzeCmdArgs(t *testing.T) {
	app := &App{}
	outputFile := ""
	outputFormat := "text"

	cmd := app.analyzeCmd(&outputFile, &outputFormat)

	// Test command properties
	if cmd.Use != "analyze [job-description-file]" {
		t.Errorf("analyzeCmd().Use = %q, want %q", cmd.Use, "analyze [job-description-file]")
	}

	if cmd.Short == "" {
		t.Error("analyzeCmd().Short is empty")
	}

	// Test with correct number of args
	err := cmd.Args(cmd, []string{"job.txt"})
	if err != nil {
		t.Errorf("analyzeCmd with 1 arg should not error, got: %v", err)
	}

	// Test with incorrect number of args
	err = cmd.Args(cmd, []string{})
	if err == nil {
		t.Error("analyzeCmd with 0 args should error")
	}
}

func TestAppClose(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	err = app.Close()
	if err != nil {
		t.Errorf("Close() failed: %v", err)
	}
}

func TestExecute(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	tests := []struct {
		name    string
		args    []string
		wantErr bool
	}{
		{
			name:    "help command",
			args:    []string{"--help"},
			wantErr: false,
		},
		{
			name:    "invalid command",
			args:    []string{"invalid-command"},
			wantErr: true,
		},
		{
			name:    "tailor with insufficient args",
			args:    []string{"tailor", "only-one-arg"},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app, err := NewApp()
			if err != nil {
				t.Fatalf("NewApp() failed: %v", err)
			}

			cmd := app.rootCmd()
			cmd.SetArgs(tt.args)

			err = cmd.Execute()
			if (err != nil) != tt.wantErr {
				t.Errorf("Execute() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAppExecute(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	// Set args for help command to avoid actual execution
	cmd := app.rootCmd()
	cmd.SetArgs([]string{"--help"})

	// Test the App's Execute method directly
	err = app.Execute()
	if err != nil {
		t.Errorf("App.Execute() failed: %v", err)
	}
}

func TestRunTailorFileErrors(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	tests := []struct {
		name       string
		resumeFile string
		jobFile    string
		wantErr    bool
	}{
		{
			name:       "missing resume file",
			resumeFile: "nonexistent-resume.txt",
			jobFile:    "examples/job.txt",
			wantErr:    true,
		},
		{
			name:       "missing job file",
			resumeFile: "examples/resume.txt",
			jobFile:    "nonexistent-job.txt",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := app.runTailor(tt.resumeFile, tt.jobFile, "", "text")
			if (err != nil) != tt.wantErr {
				t.Errorf("runTailor() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRunEvaluateFileErrors(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	tests := []struct {
		name               string
		baseResumeFile     string
		tailoredResumeFile string
		wantErr            bool
	}{
		{
			name:               "missing base resume file",
			baseResumeFile:     "nonexistent-base.txt",
			tailoredResumeFile: "examples/resume.txt",
			wantErr:            true,
		},
		{
			name:               "missing tailored resume file",
			baseResumeFile:     "examples/resume.txt",
			tailoredResumeFile: "nonexistent-tailored.txt",
			wantErr:            true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := app.runEvaluate(tt.baseResumeFile, tt.tailoredResumeFile, "", "text")
			if (err != nil) != tt.wantErr {
				t.Errorf("runEvaluate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRunAnalyzeFileErrors(t *testing.T) {
	os.Setenv("GEMINI_API_KEY", "test-api-key")
	defer os.Unsetenv("GEMINI_API_KEY")

	app, err := NewApp()
	if err != nil {
		t.Fatalf("NewApp() failed: %v", err)
	}

	// Test with missing job file
	err = app.runAnalyze("nonexistent-job.txt", "", "text")
	if err == nil {
		t.Error("runAnalyze() with missing file should error")
	}
}
