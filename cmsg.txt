refactor(ai): improve integration test structure and readability


- Validation Functions: The validation logic for the `AnalyzeJob` test has been extracted into smaller, more focused functions (e.g., `validateJobQualityScore`, `validateClarityAnalysis`).
- Simplified API Key Retrieval: The `getAPIKey` function no longer requires the `testing.T` argument, simplifying its signature and usage.
- Makefile Enhancement: The `clean` target in the `Makefile` has been updated to remove coverage profiles and other temporary build artifacts, ensuring a cleaner working directory.

