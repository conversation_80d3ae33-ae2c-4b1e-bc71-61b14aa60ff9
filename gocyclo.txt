Gocyclo calculates cyclomatic complexities of functions in Go source code.
Cyclomatic complexity is a code quality metric which can be used to identify code that needs refactoring. It measures the number of linearly independent paths through a function's source 
code.
The cyclomatic complexity of a function is calculated according to the following rules:

```
 1 is the base complexity of a function
+1 for each 'if', 'for', 'case', '&&' or '||'
```

run this to find out current complexity:
`gocyclo -over 10 ./path/to/file.go`


