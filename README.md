# Resumatter

A modern, AI-powered resume optimization tool built with Go and Google Gemini AI.

## Features

- **Resume Tailoring**: Optimize resumes for specific job descriptions
- **Resume Evaluation**: Detect fabrications and inconsistencies in tailored resumes
- **Job Analysis**: Assess job postings for quality, clarity, and inclusivity

## Architecture

This project follows modern Go idioms and best practices:

- **Clean Architecture**: Separated into packages with clear responsibilities
- **Dependency Injection**: Services are injected rather than globally accessed
- **Interface-based Design**: Easy to test and extend
- **Error Handling**: Proper error wrapping and context
- **Testing**: Comprehensive unit tests with table-driven tests

## Project Structure

```
resumatter/
├── cmd/resumatter/          # Application entrypoint
├── internal/cli/            # CLI command handling
├── pkg/
│   ├── ai/                  # AI client and prompts
│   ├── config/              # Configuration management
│   ├── fileio/              # File I/O operations
│   ├── formatter/           # Output formatting
│   ├── service/             # Business logic
│   └── types/               # Type definitions
├── examples/                # Sample files
└── Makefile                 # Build automation
```

## Prerequisites

- Go 1.24.5 or later
- Google Gemini API key

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   make deps
   ```
3. Build the application:
   ```bash
   make build
   ```

## Configuration

Set your Gemini API key as an environment variable:

```bash
export GEMINI_API_KEY="your-api-key-here"
# or
export RESUMATTER_AI_APIKEY="your-api-key-here"
```

Optional configuration:
```bash
export GEMINI_MODEL="gemini-2.0-flash"  # default
```

## Usage

### Tailor a Resume

```bash
./build/resumatter tailor examples/resume.txt examples/job.txt
```

With JSON output:
```bash
./build/resumatter tailor examples/resume.txt examples/job.txt --format json
```

Save to file:
```bash
./build/resumatter tailor examples/resume.txt examples/job.txt -o tailored-resume.txt
```

### Evaluate a Resume

```bash
./build/resumatter evaluate examples/resume.txt tailored-resume.txt
```

### Analyze a Job Description

```bash
./build/resumatter analyze examples/job.txt
```

## Development

### Make Targets

```bash
make build          # Build the binary
make clean          # Clean build artifacts
make deps           # Download dependencies
make fmt            # Format code
make vet            # Vet code
make lint           # Lint code (requires golangci-lint)
make test           # Run tests
make test-coverage  # Run tests with coverage
make dev            # Run full development workflow
make help           # Show all targets
```

### Running Examples

```bash
make run-example    # Run tailor example
make run-evaluate   # Run evaluation example
make run-analyze    # Run analysis example
```

## Output Formats

- **text**: Human-readable format (default)
- **json**: Structured JSON output

## Commands

- `tailor [resume-file] [job-description-file]`: Tailor resume for job
- `evaluate [base-resume] [tailored-resume]`: Check for fabrications
- `analyze [job-description]`: Analyze job posting quality

## Global Flags

- `-o, --output`: Output file path (default: stdout)
- `--format`: Output format: text or json (default: text)

## Getting Your API Key

1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the key and set it as an environment variable

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following the existing code style
4. Run `make dev` to ensure code quality
5. Submit a pull request

## License

This project is licensed under the Apache License 2.0. See the [LICENSE](LICENSE) file for details.
